'use client'

import { useEffect, useState } from 'react'
import { testDatabaseConnection, getSampleServiceWithDetails } from '@/lib/test-connection'

export default function TestDatabasePage() {
  const [testResult, setTestResult] = useState<any>(null)
  const [sampleService, setSampleService] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function runTests() {
      setLoading(true)
      
      // Test basic connection
      const connectionResult = await testDatabaseConnection()
      setTestResult(connectionResult)
      
      // Get sample service with details
      const serviceResult = await getSampleServiceWithDetails()
      setSampleService(serviceResult)
      
      setLoading(false)
    }
    
    runTests()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Testing database connection...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Database Connection Test</h1>
        
        {/* Connection Test Results */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Connection Test Results</h2>
          
          {testResult?.success ? (
            <div className="space-y-4">
              <div className="flex items-center text-green-600">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Database connection successful!
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-emerald-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-emerald-600">
                    {testResult.data?.services?.length || 0}
                  </div>
                  <div className="text-sm text-emerald-700">Active Services</div>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {testResult.data?.equipment?.length || 0}
                  </div>
                  <div className="text-sm text-blue-700">Equipment Types</div>
                </div>
                
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {testResult.data?.requirements?.length || 0}
                  </div>
                  <div className="text-sm text-purple-700">Equipment Requirements</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-red-600">
              <div className="flex items-center mb-2">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                Database connection failed!
              </div>
              <p className="text-sm">{testResult?.error}</p>
            </div>
          )}
        </div>
        
        {/* Sample Service Details */}
        {sampleService?.data && (
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-semibold mb-4">Sample Service Details</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">{sampleService.data.name}</h3>
                <p className="text-gray-600 text-sm">{sampleService.data.description}</p>
                <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                  <span>Duration: {sampleService.data.duration_minutes} min</span>
                  <span>Max participants: {sampleService.data.max_participants}</span>
                  <span>Base price: €{sampleService.data.base_price}</span>
                </div>
              </div>
              
              {/* Pricing Tiers */}
              {sampleService.data.pricing_tiers?.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Pricing Tiers</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    {sampleService.data.pricing_tiers.map((tier: any) => (
                      <div key={tier.id} className="bg-gray-50 p-3 rounded">
                        <div className="font-medium">{tier.tier_name}</div>
                        <div className="text-sm text-gray-600">
                          Ages {tier.min_age}-{tier.max_age || '∞'}
                        </div>
                        <div className="text-emerald-600 font-medium">€{tier.price}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Equipment Requirements */}
              {sampleService.data.service_equipment_requirements?.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Equipment Requirements</h4>
                  <div className="space-y-2">
                    {sampleService.data.service_equipment_requirements.map((req: any) => (
                      <div key={req.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                        <span>{req.equipment.name}</span>
                        <span className="text-sm text-gray-600">
                          {req.capacity_per_participant} per participant
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
