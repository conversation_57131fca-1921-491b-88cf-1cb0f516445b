"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock, MessageSquare, Trash2 } from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

interface Notification {
	id: string;
	type: "booking" | "payment" | "system" | "message" | "weather";
	title: string;
	message: string;
	timestamp: string;
	read: boolean;
	priority: "low" | "medium" | "high";
}

const AdminNotifications = () => {
	const [notifications, setNotifications] = useState<Notification[]>([
		{
			id: "1",
			type: "booking",
			title: "Nouvelle réservation",
			message: '<PERSON> a réservé "Découverte de la Mangrove" pour le 01/02/2025',
			timestamp: "2025-01-15T10:30:00Z",
			read: false,
			priority: "medium",
		},
		{
			id: "2",
			type: "payment",
			title: "Paiement reçu",
			message: "Paiement de €180 reçu pour la réservation #RES-001",
			timestamp: "2025-01-15T09:15:00Z",
			read: false,
			priority: "high",
		},
		{
			id: "3",
			type: "system",
			title: "Maintenance programmée",
			message: "Maintenance du système prévue le 20/01/2025 de 02:00 à 04:00",
			timestamp: "2025-01-14T16:00:00Z",
			read: true,
			priority: "low",
		},
		{
			id: "4",
			type: "message",
			title: "Message client",
			message: "Jean Martin a envoyé un message concernant sa réservation",
			timestamp: "2025-01-14T14:30:00Z",
			read: true,
			priority: "medium",
		},
		{
			id: "5",
			type: "weather",
			title: "Alerte météo",
			message: "Conditions météorologiques défavorables prévues pour demain",
			timestamp: "2025-01-19T14:30:00Z",
			read: false,
			priority: "high",
		},
	]);

	const [filter, setFilter] = useState<string>("all");

	const getTypeIcon = (type: string) => {
		switch (type) {
			case "booking":
				return <Bell className="h-5 w-5 text-blue-600" />;
			case "payment":
				return <CheckCircle className="h-5 w-5 text-green-600" />;
			case "system":
				return <AlertCircle className="h-5 w-5 text-orange-600" />;
			case "message":
				return <MessageSquare className="h-5 w-5 text-purple-600" />;
			default:
				return <Bell className="h-5 w-5 text-gray-600" />;
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case "high":
				return "bg-red-100 text-red-800";
			case "medium":
				return "bg-yellow-100 text-yellow-800";
			case "low":
				return "bg-green-100 text-green-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getPriorityText = (priority: string) => {
		switch (priority) {
			case "high":
				return "Haute";
			case "medium":
				return "Moyenne";
			case "low":
				return "Basse";
			default:
				return priority;
		}
	};

	const markAsRead = (id: string) => {
		setNotifications(notifications.map((notif) => (notif.id === id ? { ...notif, read: true } : notif)));
	};

	const deleteNotification = (id: string) => {
		setNotifications(notifications.filter((notif) => notif.id !== id));
	};

	const markAllAsRead = () => {
		setNotifications(notifications.map((notif) => ({ ...notif, read: true })));
	};

	const filteredNotifications = notifications.filter((notif) => {
		if (filter === "all") return true;
		if (filter === "unread") return !notif.read;
		return notif.type === filter;
	});

	const unreadCount = notifications.filter((notif) => !notif.read).length;

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						Notifications
						{unreadCount > 0 && (
							<span className="ml-2 bg-red-500 text-white text-sm px-2 py-1 rounded-full">
								{unreadCount}
							</span>
						)}
					</h1>
					<p className="text-gray-600">Gérez vos notifications et alertes</p>
				</div>
				<Button onClick={markAllAsRead} disabled={unreadCount === 0}>
					Tout marquer comme lu
				</Button>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
				<div className="flex flex-wrap gap-2">
					<button
						onClick={() => setFilter("all")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "all"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Toutes ({notifications.length})
					</button>
					<button
						onClick={() => setFilter("unread")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "unread"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Non lues ({unreadCount})
					</button>
					<button
						onClick={() => setFilter("booking")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "booking"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Réservations
					</button>
					<button
						onClick={() => setFilter("payment")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "payment"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Paiements
					</button>
					<button
						onClick={() => setFilter("system")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "system"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Système
					</button>
					<button
						onClick={() => setFilter("message")}
						className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
							filter === "message"
								? "bg-emerald-100 text-emerald-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Messages
					</button>
				</div>
			</div>

			{/* Notifications List */}
			<div className="space-y-4">
				{filteredNotifications.map((notification) => (
					<div
						key={notification.id}
						className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${
							!notification.read ? "border-l-4 border-l-emerald-500" : ""
						}`}
					>
						<div className="flex items-start justify-between">
							<div className="flex items-start gap-4 flex-1">
								<div className="bg-gray-100 p-2 rounded-lg">{getTypeIcon(notification.type)}</div>
								<div className="flex-1">
									<div className="flex items-center gap-2 mb-1">
										<h3
											className={`text-lg font-medium ${
												!notification.read ? "text-gray-900" : "text-gray-700"
											}`}
										>
											{notification.title}
										</h3>
										<span
											className={`px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(
												notification.priority
											)}`}
										>
											{getPriorityText(notification.priority)}
										</span>
										{!notification.read && (
											<span className="w-2 h-2 bg-emerald-500 rounded-full"></span>
										)}
									</div>
									<p className="text-gray-600 mb-2">{notification.message}</p>
									<div className="flex items-center gap-2 text-sm text-gray-500">
										<Clock className="h-4 w-4" />
										{new Date(notification.timestamp).toLocaleString("fr-FR")}
									</div>
								</div>
							</div>
							<div className="flex gap-2">
								{!notification.read && (
									<button
										onClick={() => markAsRead(notification.id)}
										className="p-2 text-gray-400 hover:text-emerald-600"
										title="Marquer comme lu"
									>
										<CheckCircle className="h-4 w-4" />
									</button>
								)}
								<button
									onClick={() => deleteNotification(notification.id)}
									className="p-2 text-gray-400 hover:text-red-600"
									title="Supprimer"
								>
									<Trash2 className="h-4 w-4" />
								</button>
							</div>
						</div>
					</div>
				))}
			</div>

			{filteredNotifications.length === 0 && (
				<div className="text-center py-12">
					<Bell className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">Aucune notification</h3>
					<p className="mt-1 text-sm text-gray-500">Aucune notification ne correspond à vos critères.</p>
				</div>
			)}
		</div>
	);
};

export default AdminNotifications;
