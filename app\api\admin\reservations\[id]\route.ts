import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { withAdminAuth, logAdminAction } from '@/lib/admin-auth'
import { Database } from '@/lib/types/database'

type Reservation = Database['public']['Tables']['reservations']['Row']
type ReservationUpdate = Database['public']['Tables']['reservations']['Update']

// GET /api/admin/reservations/[id] - Get single reservation with full details
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const reservationId = params.id

    const { data: reservation, error } = await supabaseAdmin
      .from('reservations')
      .select(`
        *,
        service:services (
          id,
          name,
          description,
          duration_minutes,
          category,
          base_price,
          max_participants,
          image_url,
          features
        ),
        customer:customers (
          id,
          date_of_birth,
          nationality,
          emergency_contact_name,
          emergency_contact_phone,
          dietary_restrictions,
          medical_conditions
        ),
        customer_profile:profiles!reservations_customer_id_fkey (
          id,
          email,
          first_name,
          last_name,
          phone
        ),
        assigned_employee:employees (
          id,
          first_name,
          last_name,
          email,
          phone,
          role,
          skills,
          languages
        ),
        payments (
          id,
          amount,
          currency,
          status,
          payment_method,
          payment_date,
          payment_intent_id,
          failure_reason
        ),
        refunds (
          id,
          refund_amount,
          refund_reason,
          refund_method,
          status,
          processed_by,
          processed_at
        ),
        customer_feedback (
          id,
          rating,
          review_text,
          service_quality_rating,
          staff_rating,
          equipment_rating,
          would_recommend,
          is_public,
          response_text,
          responded_by,
          responded_at,
          created_at
        ),
        reservation_status_history (
          id,
          old_status,
          new_status,
          changed_by,
          change_reason,
          automated_change,
          created_at
        ),
        time_slots (
          id,
          start_time,
          end_time,
          available_spots,
          equipment_reservations (
            id,
            reserved_capacity,
            status,
            equipment:equipment (
              id,
              name,
              total_capacity
            )
          )
        )
      `)
      .eq('id', reservationId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Reservation not found' }, { status: 404 })
      }
      console.error('Error fetching reservation:', error)
      return NextResponse.json({ error: 'Failed to fetch reservation' }, { status: 500 })
    }

    // Get customer's reservation history for context
    const { data: customerHistory } = await supabaseAdmin
      .from('reservations')
      .select(`
        id,
        reservation_number,
        start_time,
        status,
        total_amount,
        service:services (name)
      `)
      .eq('customer_id', reservation.customer_id)
      .neq('id', reservationId)
      .order('start_time', { ascending: false })
      .limit(5)

    // Calculate financial summary
    const payments = reservation.payments || []
    const refunds = reservation.refunds || []
    
    const totalPaid = payments
      .filter(p => p.status === 'succeeded')
      .reduce((sum, p) => sum + p.amount, 0)
    
    const totalRefunded = refunds
      .filter(r => r.status === 'completed')
      .reduce((sum, r) => sum + r.refund_amount, 0)

    const netAmount = totalPaid - totalRefunded

    // Check for conflicts or issues
    const issues = []
    
    // Check if employee is available
    if (reservation.assigned_employee_id) {
      const { data: employeeTimeOff } = await supabaseAdmin
        .from('employee_time_off')
        .select('id, reason')
        .eq('employee_id', reservation.assigned_employee_id)
        .lte('start_date', reservation.start_time)
        .gte('end_date', reservation.start_time)
        .eq('status', 'approved')

      if (employeeTimeOff && employeeTimeOff.length > 0) {
        issues.push({
          type: 'employee_unavailable',
          message: 'Assigned employee has approved time off during this period',
          severity: 'high'
        })
      }
    }

    // Check equipment availability
    const equipmentReservations = reservation.time_slots?.equipment_reservations || []
    for (const equipRes of equipmentReservations) {
      if (equipRes.reserved_capacity > (equipRes.equipment?.total_capacity || 0)) {
        issues.push({
          type: 'equipment_overbooked',
          message: `Equipment ${equipRes.equipment?.name} is overbooked`,
          severity: 'high'
        })
      }
    }

    // Check if service is still active
    if (!reservation.service?.id) {
      issues.push({
        type: 'service_inactive',
        message: 'Associated service is no longer active',
        severity: 'medium'
      })
    }

    return NextResponse.json({
      reservation: {
        ...reservation,
        financial: {
          totalAmount: reservation.total_amount || 0,
          totalPaid,
          totalRefunded,
          netAmount,
          paymentStatus: payments.length > 0 ? payments[payments.length - 1].status : 'pending',
          outstandingBalance: (reservation.total_amount || 0) - totalPaid
        },
        issues,
        customerHistory: customerHistory || []
      }
    })
  } catch (error) {
    console.error('Reservation GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'reservations:read')

// PUT /api/admin/reservations/[id] - Update single reservation
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const reservationId = params.id
    const updates: ReservationUpdate & { status_change_reason?: string } = await request.json()

    // Extract status change reason if provided
    const { status_change_reason, ...reservationUpdates } = updates

    // Get current reservation for audit log
    const { data: currentReservation } = await supabaseAdmin
      .from('reservations')
      .select('*')
      .eq('id', reservationId)
      .single()

    if (!currentReservation) {
      return NextResponse.json({ error: 'Reservation not found' }, { status: 404 })
    }

    // Add admin confirmation data if status is being confirmed
    if (reservationUpdates.status === 'confirmed' && currentReservation.status !== 'confirmed') {
      reservationUpdates.confirmed_by = user.id
      reservationUpdates.confirmed_at = new Date().toISOString()
    }

    // Update reservation
    const { data: updatedReservation, error } = await supabaseAdmin
      .from('reservations')
      .update(reservationUpdates)
      .eq('id', reservationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating reservation:', error)
      return NextResponse.json({ error: 'Failed to update reservation' }, { status: 500 })
    }

    // Log status change manually if reason provided
    if (status_change_reason && currentReservation.status !== updatedReservation.status) {
      await supabaseAdmin
        .from('reservation_status_history')
        .insert({
          reservation_id: reservationId,
          old_status: currentReservation.status,
          new_status: updatedReservation.status,
          changed_by: user.id,
          change_reason: status_change_reason,
          automated_change: false
        })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'UPDATE',
      'reservations',
      reservationId,
      currentReservation,
      updatedReservation,
      request
    )

    return NextResponse.json({ reservation: updatedReservation })
  } catch (error) {
    console.error('Reservation PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'reservations:write')

// DELETE /api/admin/reservations/[id] - Cancel single reservation
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const reservationId = params.id
    const { reason, refund_amount }: { reason?: string, refund_amount?: number } = await request.json()

    // Get reservation for audit log
    const { data: reservationToCancel } = await supabaseAdmin
      .from('reservations')
      .select('*')
      .eq('id', reservationId)
      .single()

    if (!reservationToCancel) {
      return NextResponse.json({ error: 'Reservation not found' }, { status: 404 })
    }

    if (reservationToCancel.status === 'cancelled') {
      return NextResponse.json({ error: 'Reservation is already cancelled' }, { status: 400 })
    }

    // Cancel reservation
    const { error } = await supabaseAdmin
      .from('reservations')
      .update({ 
        status: 'cancelled',
        admin_notes: reason ? `Cancelled by admin: ${reason}` : 'Cancelled by admin'
      })
      .eq('id', reservationId)

    if (error) {
      console.error('Error cancelling reservation:', error)
      return NextResponse.json({ error: 'Failed to cancel reservation' }, { status: 500 })
    }

    // Process refund if amount specified
    if (refund_amount && refund_amount > 0) {
      // Get the latest successful payment
      const { data: payment } = await supabaseAdmin
        .from('payments')
        .select('*')
        .eq('reservation_id', reservationId)
        .eq('status', 'succeeded')
        .order('payment_date', { ascending: false })
        .limit(1)
        .single()

      if (payment) {
        await supabaseAdmin
          .from('refunds')
          .insert({
            payment_id: payment.id,
            reservation_id: reservationId,
            refund_amount,
            refund_reason: reason || 'Admin cancellation',
            refund_method: payment.payment_method,
            status: 'pending',
            processed_by: user.id
          })
      }
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'CANCEL',
      'reservations',
      reservationId,
      reservationToCancel,
      { ...reservationToCancel, status: 'cancelled' },
      request
    )

    return NextResponse.json({ 
      message: 'Reservation cancelled successfully',
      reservationId,
      refundProcessed: refund_amount ? true : false
    })
  } catch (error) {
    console.error('Reservation DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'reservations:write')
