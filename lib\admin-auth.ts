import { NextRequest } from 'next/server'
import { supabaseAdmin } from './supabase'

export interface AdminUser {
  id: string
  email: string
  role: string
}

export interface AdminAuthResult {
  success: boolean
  user?: AdminUser
  error?: string
}

/**
 * Verify admin authentication and authorization
 */
export async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false, error: 'Missing or invalid authorization header' }
    }

    const token = authHeader.substring(7)

    // Verify JWT token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      return { success: false, error: 'Invalid or expired token' }
    }

    // Check if user has admin, manager, or employee role from user metadata
    const role = user.user_metadata?.role
    if (!['admin', 'manager', 'employee'].includes(role)) {
      return { success: false, error: 'Insufficient permissions' }
    }

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email || '',
        role: role
      }
    }
  } catch (error) {
    console.error('Admin auth verification error:', error)
    return { success: false, error: 'Authentication verification failed' }
  }
}

/**
 * Middleware wrapper for admin routes
 */
export function withAdminAuth(
  handler: (request: NextRequest, user: AdminUser, context?: any) => Promise<Response>,
  requiredPermission?: string
) {
  return async (request: NextRequest, context?: any) => {
    const authResult = await verifyAdminAuth(request)

    if (!authResult.success || !authResult.user) {
      return new Response(
        JSON.stringify({ error: authResult.error || 'Authentication failed' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check specific permission if required
    if (requiredPermission) {
      const { hasPermission } = await import('./permissions')
      if (!hasPermission(authResult.user.role as any, requiredPermission as any)) {
        return new Response(
          JSON.stringify({ error: 'Insufficient permissions for this operation' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        )
      }
    }

    return handler(request, authResult.user, context)
  }
}


