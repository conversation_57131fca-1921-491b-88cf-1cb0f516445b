"use client";

import {
    Bell,
    Calendar,
    LayoutDashboard,
    Settings,
    UserCheck,
    UserCircle,
    Users,
    Wrench
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const AdminSidebar = () => {
  const pathname = usePathname();

  if (pathname === '/admin/login') return null;

  const navigation = [
    { name: 'Tableau de bord', href: '/admin/dashboard', icon: LayoutDashboard },
    { name: 'Services', href: '/admin/services', icon: Wrench },
    { name: 'Réservations', href: '/admin/reservations', icon: Calendar },
    { name: 'Planning', href: '/admin/schedule', icon: UserCheck },
    { name: 'Clients', href: '/admin/clients', icon: UserCircle },
    { name: 'Utilisateurs', href: '/admin/users', icon: Users },
    { name: 'Notifications', href: '/admin/notifications', icon: Bell },
    { name: 'Para<PERSON><PERSON><PERSON>', href: '/admin/settings', icon: Settings }
  ];

  return (
    <div className="bg-gray-900 text-white w-64 min-h-screen">
      <nav className="mt-8">
        <div className="px-4">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      isActive
                        ? 'bg-emerald-600 text-white'
                        : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    }`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </nav>
    </div>
  );
};

export default AdminSidebar;
