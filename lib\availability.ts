import { supabase } from './supabase'
import { TimeSlotWithAvailability, AvailabilityRequest, AvailabilityResponse } from './types'
import { businessConfig } from './env'

/**
 * Calculate equipment capacity for a service at a specific time
 */
export async function calculateEquipmentCapacity(
  serviceId: string,
  startTime: string,
  endTime: string,
  participantCount: number
): Promise<{ available: boolean; maxCapacity: number; currentReservations: number }> {
  try {
    // Get service equipment requirements
    const { data: serviceEquipment, error: equipmentError } = await supabase
      .from('service_equipment_requirements')
      .select(`
        capacity_per_participant,
        equipment (
          id,
          name,
          total_capacity
        )
      `)
      .eq('service_id', serviceId)

    if (equipmentError) throw equipmentError

    if (!serviceEquipment || serviceEquipment.length === 0) {
      // No equipment requirements, use service max participants
      const { data: service } = await supabase
        .from('services')
        .select('max_participants')
        .eq('id', serviceId)
        .single()

      return {
        available: participantCount <= (service?.max_participants || 0),
        maxCapacity: service?.max_participants || 0,
        currentReservations: 0
      }
    }

    // Calculate capacity for each equipment type
    let minAvailableCapacity = Infinity
    let totalCurrentReservations = 0

    for (const req of serviceEquipment) {
      const equipment = req.equipment
      const capacityNeeded = participantCount * req.capacity_per_participant

      // Get existing reservations for this equipment during the time period
      const { data: reservations, error: reservationError } = await supabase
        .from('equipment_reservations')
        .select('quantity_reserved')
        .eq('equipment_id', equipment.id)
        .gte('start_time', startTime)
        .lte('end_time', endTime)

      if (reservationError) throw reservationError

      const currentReserved = reservations?.reduce((sum, res) => sum + res.quantity_reserved, 0) || 0
      const availableCapacity = equipment.total_capacity - currentReserved
      const maxParticipants = Math.floor(availableCapacity / req.capacity_per_participant)

      minAvailableCapacity = Math.min(minAvailableCapacity, maxParticipants)
      totalCurrentReservations += currentReserved
    }

    return {
      available: participantCount <= minAvailableCapacity,
      maxCapacity: minAvailableCapacity === Infinity ? 0 : minAvailableCapacity,
      currentReservations: totalCurrentReservations
    }
  } catch (error) {
    console.error('Error calculating equipment capacity:', error)
    return { available: false, maxCapacity: 0, currentReservations: 0 }
  }
}

/**
 * Get available time slots for a service on a specific date
 */
export async function getAvailableTimeSlots(
  serviceId: string,
  date: string,
  participantCount: number = 1
): Promise<TimeSlotWithAvailability[]> {
  try {
    // Get service details for buffer time
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('duration_minutes, buffer_time_minutes, max_participants')
      .eq('id', serviceId)
      .single()

    if (serviceError) throw serviceError

    // Get time slots for the service on the specified date
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const { data: timeSlots, error: slotsError } = await supabase
      .from('time_slots')
      .select('*')
      .eq('service_id', serviceId)
      .gte('start_time', startOfDay.toISOString())
      .lte('start_time', endOfDay.toISOString())
      .eq('status', 'available')
      .order('start_time')

    if (slotsError) throw slotsError

    // Calculate availability for each time slot
    const slotsWithAvailability: TimeSlotWithAvailability[] = []

    for (const slot of timeSlots || []) {
      const capacity = await calculateEquipmentCapacity(
        serviceId,
        slot.start_time,
        slot.end_time,
        participantCount
      )

      slotsWithAvailability.push({
        ...slot,
        available_capacity: capacity.maxCapacity,
        is_available: capacity.available && capacity.maxCapacity >= participantCount
      })
    }

    return slotsWithAvailability
  } catch (error) {
    console.error('Error getting available time slots:', error)
    return []
  }
}

/**
 * Check availability for multiple dates
 */
export async function checkAvailability(
  request: AvailabilityRequest
): Promise<AvailabilityResponse> {
  const timeSlots = await getAvailableTimeSlots(
    request.serviceId,
    request.date,
    request.participantCount
  )

  return {
    date: request.date,
    timeSlots
  }
}

/**
 * Generate time slots for a service (utility function for admin)
 */
export async function generateTimeSlots(
  serviceId: string,
  date: string,
  startHour: number = 8,
  endHour: number = 18,
  intervalMinutes: number = 60
): Promise<void> {
  try {
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('duration_minutes, buffer_time_minutes')
      .eq('id', serviceId)
      .single()

    if (serviceError) throw serviceError

    const slots = []
    const baseDate = new Date(date)
    
    for (let hour = startHour; hour < endHour; hour += intervalMinutes / 60) {
      const startTime = new Date(baseDate)
      startTime.setHours(Math.floor(hour), (hour % 1) * 60, 0, 0)
      
      const endTime = new Date(startTime)
      endTime.setMinutes(endTime.getMinutes() + service.duration_minutes)
      
      // Check if end time is within business hours
      if (endTime.getHours() <= endHour) {
        slots.push({
          service_id: serviceId,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          status: 'available' as const
        })
      }
    }

    if (slots.length > 0) {
      const { error: insertError } = await supabase
        .from('time_slots')
        .insert(slots)

      if (insertError) throw insertError
    }
  } catch (error) {
    console.error('Error generating time slots:', error)
    throw error
  }
}

/**
 * Reserve equipment for a booking
 */
export async function reserveEquipment(
  serviceId: string,
  timeSlotId: string,
  participantCount: number,
  reservationId: string
): Promise<boolean> {
  try {
    // Get time slot details
    const { data: timeSlot, error: slotError } = await supabase
      .from('time_slots')
      .select('start_time, end_time')
      .eq('id', timeSlotId)
      .single()

    if (slotError) throw slotError

    // Get service equipment requirements
    const { data: serviceEquipment, error: equipmentError } = await supabase
      .from('service_equipment_requirements')
      .select(`
        capacity_per_participant,
        equipment (
          id,
          name,
          total_capacity
        )
      `)
      .eq('service_id', serviceId)

    if (equipmentError) throw equipmentError

    // Create equipment reservations
    const reservations = []
    for (const req of serviceEquipment || []) {
      const quantityNeeded = participantCount * req.capacity_per_participant
      
      reservations.push({
        equipment_id: req.equipment.id,
        reservation_id: reservationId,
        start_time: timeSlot.start_time,
        end_time: timeSlot.end_time,
        quantity_reserved: quantityNeeded
      })
    }

    if (reservations.length > 0) {
      const { error: reservationError } = await supabase
        .from('equipment_reservations')
        .insert(reservations)

      if (reservationError) throw reservationError
    }

    return true
  } catch (error) {
    console.error('Error reserving equipment:', error)
    return false
  }
}
